<?php
// Vérifier l'authentification
require_once '../includes/auth_check_coordinateur.php';

// Récupérer les informations du coordinateur depuis la session
$userName = $_SESSION['user']['username'] ?? 'Coordinateur';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$filiereName = $_SESSION['user']['filiere_name'] ?? 'Non spécifié';
$filiereId = $_SESSION['user']['filiere_id'] ?? 1;

// Construire le nom complet
$fullName = $prenom . ' ' . $nom;
if (trim($fullName) === '') {
    $fullName = $userName;
}

// Inclure le modèle des visites et enregistrer la visite
require_once '../../model/visitsModel.php';
recordVisit('coordinateur', 'create_vacataire_account');

// Page title
$pageTitle = "Create Vacataire Account";
$currentPage = "create-vacataire-account.php";

require_once "../../config/constants.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - UniAdmin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        /* Page-specific styles */
        .page-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 0.95rem;
            margin-bottom: 2rem;
        }

        .form-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: none;
            margin-bottom: 2rem;
        }

        .form-card .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px 12px 0 0;
            padding: 1.5rem;
            border: none;
        }

        .form-card .card-body {
            padding: 2rem;
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .form-section-title {
            color: #495057;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e9ecef;
        }

        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 1px solid #e0e6ed;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-create {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-cancel {
            background: #6c757d;
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-cancel:hover {
            background: #5a6268;
            color: white;
        }

        .alert {
            border-radius: 8px;
            border: none;
            padding: 1rem 1.5rem;
        }

        .alert-success {
            background-color: #d1fae5;
            color: #065f46;
        }

        .alert-danger {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .required {
            color: #dc3545;
        }

        .info-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-box .info-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .info-box .info-text {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 0;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="row mb-4">
                    <div class="col">
                        <h1 class="page-title">
                            <i class="fas fa-user-plus me-2"></i><?php echo $pageTitle; ?>
                        </h1>
                        <p class="page-subtitle">Create user accounts for part-time lecturers (vacataires) in your program</p>
                    </div>
                </div>

                <!-- Alert Container -->
                <div class="alert-container" id="alertContainer"></div>

                <!-- Information Box -->
                <div class="info-box">
                    <div class="info-title">
                        <i class="bi bi-info-circle me-2"></i>Account Creation Information
                    </div>
                    <p class="info-text">
                        When you create a vacataire account, the system will automatically generate a secure password reset link
                        and send it to the provided email address. The vacataire will receive instructions to set their password
                        and access the platform. The CNI will be used as the username for login.
                    </p>
                </div>

                <!-- Create Account Form -->
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="card form-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-tie me-2"></i>Vacataire Account Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="createVacataireForm">
                                    <!-- Personal Information Section -->
                                    <div class="form-section">
                                        <h6 class="form-section-title">Personal Information</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label for="CNI" class="form-label">CNI <span class="required">*</span></label>
                                                <input type="text" class="form-control" id="CNI" name="CNI" required
                                                       placeholder="Enter CNI number" maxlength="20">
                                                <div class="form-text">This will be used as the username for login</div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="email" class="form-label">Email Address <span class="required">*</span></label>
                                                <input type="email" class="form-control" id="email" name="email" required
                                                       placeholder="Enter email address">
                                                <div class="form-text">Password reset instructions will be sent here</div>
                                            </div>
                                        </div>
                                        <div class="row g-3 mt-2">
                                            <div class="col-md-6">
                                                <label for="nom" class="form-label">Last Name <span class="required">*</span></label>
                                                <input type="text" class="form-control" id="nom" name="nom" required
                                                       placeholder="Enter last name" maxlength="50">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="prenom" class="form-label">First Name <span class="required">*</span></label>
                                                <input type="text" class="form-control" id="prenom" name="prenom" required
                                                       placeholder="Enter first name" maxlength="50">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Account Information Section -->
                                    <div class="form-section">
                                        <h6 class="form-section-title">Account Information</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label for="role" class="form-label">Role</label>
                                                <input type="text" class="form-control" id="role" name="role" value="Vacataire" readonly>
                                                <div class="form-text">Role is automatically set to Vacataire</div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="filiere" class="form-label">Program (Filière)</label>
                                                <input type="text" class="form-control" id="filiere" name="filiere"
                                                       value="<?php echo htmlspecialchars($filiereName); ?>" readonly>
                                                <div class="form-text">Account will be associated with your program</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Form Actions -->
                                    <div class="d-flex justify-content-end gap-3 mt-4">
                                        <button type="button" class="btn btn-cancel" onclick="resetForm()">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </button>
                                        <button type="submit" class="btn btn-create" id="submitBtn">
                                            <span class="btn-text">
                                                <i class="fas fa-user-plus me-2"></i>Create Account
                                            </span>
                                            <span class="btn-loading d-none">
                                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                                Creating...
                                            </span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('createVacataireForm');
            const submitBtn = document.getElementById('submitBtn');
            const alertContainer = document.getElementById('alertContainer');

            // Form submission handler
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Validate form
                if (!validateForm()) {
                    return;
                }

                // Show loading state
                showLoading(true);

                // Prepare form data
                const formData = new FormData(form);
                const data = {
                    CNI: formData.get('CNI').trim(),
                    email: formData.get('email').trim(),
                    nom: formData.get('nom').trim(),
                    prenom: formData.get('prenom').trim(),
                    role: 'vacataire'
                };

                // Send request to create account
                fetch('<?php echo BASE_URL; ?>/route/createVacataireAccountRoute.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    showLoading(false);

                    if (data.success) {
                        showAlert('success', data.message || 'Vacataire account created successfully! Password reset instructions have been sent to the provided email address.');
                        resetForm();
                    } else {
                        showAlert('danger', data.error || 'An error occurred while creating the account. Please try again.');
                    }
                })
                .catch(error => {
                    showLoading(false);
                    console.error('Error:', error);
                    showAlert('danger', 'A network error occurred. Please check your connection and try again.');
                });
            });

            // Form validation
            function validateForm() {
                const cni = document.getElementById('CNI').value.trim();
                const email = document.getElementById('email').value.trim();
                const nom = document.getElementById('nom').value.trim();
                const prenom = document.getElementById('prenom').value.trim();

                // Clear previous validation states
                clearValidationStates();

                let isValid = true;

                // Validate CNI
                if (!cni) {
                    setFieldError('CNI', 'CNI is required');
                    isValid = false;
                } else if (cni.length < 5) {
                    setFieldError('CNI', 'CNI must be at least 5 characters long');
                    isValid = false;
                }

                // Validate email
                if (!email) {
                    setFieldError('email', 'Email address is required');
                    isValid = false;
                } else if (!isValidEmail(email)) {
                    setFieldError('email', 'Please enter a valid email address');
                    isValid = false;
                }

                // Validate nom
                if (!nom) {
                    setFieldError('nom', 'Last name is required');
                    isValid = false;
                } else if (nom.length < 2) {
                    setFieldError('nom', 'Last name must be at least 2 characters long');
                    isValid = false;
                }

                // Validate prenom
                if (!prenom) {
                    setFieldError('prenom', 'First name is required');
                    isValid = false;
                } else if (prenom.length < 2) {
                    setFieldError('prenom', 'First name must be at least 2 characters long');
                    isValid = false;
                }

                return isValid;
            }

            // Helper functions
            function setFieldError(fieldId, message) {
                const field = document.getElementById(fieldId);
                field.classList.add('is-invalid');

                // Remove existing error message
                const existingError = field.parentNode.querySelector('.invalid-feedback');
                if (existingError) {
                    existingError.remove();
                }

                // Add new error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            }

            function clearValidationStates() {
                const fields = ['CNI', 'email', 'nom', 'prenom'];
                fields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    field.classList.remove('is-invalid', 'is-valid');

                    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
                    if (errorDiv) {
                        errorDiv.remove();
                    }
                });
            }

            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            function showLoading(show) {
                const btnText = submitBtn.querySelector('.btn-text');
                const btnLoading = submitBtn.querySelector('.btn-loading');

                if (show) {
                    btnText.classList.add('d-none');
                    btnLoading.classList.remove('d-none');
                    submitBtn.disabled = true;
                } else {
                    btnText.classList.remove('d-none');
                    btnLoading.classList.add('d-none');
                    submitBtn.disabled = false;
                }
            }

            function showAlert(type, message) {
                // Remove existing alerts
                alertContainer.innerHTML = '';

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                alertContainer.appendChild(alertDiv);

                // Scroll to alert
                alertContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

                // Auto-hide success alerts after 5 seconds
                if (type === 'success') {
                    setTimeout(() => {
                        const alert = alertContainer.querySelector('.alert');
                        if (alert) {
                            const bsAlert = new bootstrap.Alert(alert);
                            bsAlert.close();
                        }
                    }, 5000);
                }
            }

            // Real-time validation
            document.getElementById('CNI').addEventListener('input', function() {
                this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
            });

            document.getElementById('email').addEventListener('blur', function() {
                if (this.value && !isValidEmail(this.value)) {
                    setFieldError('email', 'Please enter a valid email address');
                } else {
                    this.classList.remove('is-invalid');
                    const errorDiv = this.parentNode.querySelector('.invalid-feedback');
                    if (errorDiv) {
                        errorDiv.remove();
                    }
                }
            });
        });

        // Reset form function
        function resetForm() {
            const form = document.getElementById('createVacataireForm');
            form.reset();

            // Clear validation states
            const fields = ['CNI', 'email', 'nom', 'prenom'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                field.classList.remove('is-invalid', 'is-valid');

                const errorDiv = field.parentNode.querySelector('.invalid-feedback');
                if (errorDiv) {
                    errorDiv.remove();
                }
            });
        }
    </script>
</body>
</html>
