<?php
/**
 * Route for creating vacataire user accounts by coordinators
 */

// Set headers for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Include the controller
require_once __DIR__ . '/../controller/createVacataireAccountController.php';

// Handle the request
handleRequest();
?>
