<?php
/**
 * Controller for creating vacataire user accounts by coordinators
 */

// Include required files
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../model/userModel.php';
require_once __DIR__ . '/../model/authModel.php';
require_once __DIR__ . '/../utils/emailSender.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * JSON response helper function
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Create a vacataire user account
 */
function createVacataireAccountAPI() {
    // Check if user is authenticated and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Accès non autorisé. Seuls les coordinateurs peuvent créer des comptes vacataires.'], 403);
    }

    // Get JSON data
    $data = json_decode(file_get_contents("php://input"), true);
    if (!$data) {
        jsonResponse(['error' => 'Données JSON invalides ou manquantes'], 400);
    }

    // Validate required fields
    $required_fields = ['CNI', 'email', 'nom', 'prenom'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            jsonResponse(['error' => "Le champ '$field' est obligatoire"], 400);
        }
    }

    // Sanitize and validate input data
    $cni = strtoupper(trim($data['CNI']));
    $email = strtolower(trim($data['email']));
    $nom = trim($data['nom']);
    $prenom = trim($data['prenom']);
    $role = 'vacataire'; // Fixed role

    // Validate CNI format (alphanumeric, 5-20 characters)
    if (!preg_match('/^[A-Z0-9]{5,20}$/', $cni)) {
        jsonResponse(['error' => 'Le CNI doit contenir entre 5 et 20 caractères alphanumériques'], 400);
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['error' => 'Format d\'email invalide'], 400);
    }

    // Validate name lengths
    if (strlen($nom) < 2 || strlen($nom) > 50) {
        jsonResponse(['error' => 'Le nom doit contenir entre 2 et 50 caractères'], 400);
    }

    if (strlen($prenom) < 2 || strlen($prenom) > 50) {
        jsonResponse(['error' => 'Le prénom doit contenir entre 2 et 50 caractères'], 400);
    }

    // Check if user with this CNI already exists
    $existingUser = getUserByUsername($cni);
    if ($existingUser && !isset($existingUser['error'])) {
        jsonResponse(['error' => 'Un utilisateur avec ce CNI existe déjà'], 409);
    }

    // Generate a temporary password and hash it
    $tempPassword = bin2hex(random_bytes(8));
    $hashedPassword = password_hash($tempPassword, PASSWORD_DEFAULT);

    // Create the user account
    $result = createUser($cni, $hashedPassword, $role);

    if (isset($result['error'])) {
        jsonResponse(['error' => 'Erreur lors de la création du compte: ' . $result['error']], 500);
    }

    // Generate password reset token
    $token = bin2hex(random_bytes(32));
    $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

    // Store the password reset token
    $tokenResult = storePasswordResetToken($cni, $token, $expiry);

    if (isset($tokenResult['error'])) {
        // Account was created but token storage failed
        jsonResponse(['error' => 'Compte créé mais erreur lors de la génération du lien de réinitialisation: ' . $tokenResult['error']], 500);
    }

    // Send password initialization email
    $emailResult = sendVacataireAccountEmail($email, $nom, $prenom, $cni, $token);

    if (!$emailResult['success']) {
        // Account and token were created but email failed
        jsonResponse([
            'success' => true,
            'message' => 'Compte vacataire créé avec succès, mais l\'email n\'a pas pu être envoyé. Veuillez contacter l\'administrateur pour obtenir le lien de réinitialisation.',
            'warning' => 'Email non envoyé: ' . $emailResult['message']
        ], 201);
    }

    // Everything successful
    jsonResponse([
        'success' => true,
        'message' => 'Compte vacataire créé avec succès. Un email avec les instructions de connexion a été envoyé à ' . $email,
        'username' => $cni
    ], 201);
}

/**
 * Send account creation email to vacataire
 */
function sendVacataireAccountEmail($email, $nom, $prenom, $username, $token) {
    // Get coordinator info for email context
    $coordinatorName = $_SESSION['user']['prenom'] . ' ' . $_SESSION['user']['nom'];
    $filiereName = $_SESSION['user']['filiere_name'] ?? 'Non spécifié';

    // Build password reset link
    $host = $_SERVER['HTTP_HOST'];
    require_once __DIR__ . '/../config/constants.php';
    $resetLink = "http://$host" . BASE_URL . "/view/initialize-password.php?token=$token";

    $subject = "Création de votre compte vacataire - ENSAH";

    $message = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            .important { color: #e74c3c; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Bienvenue sur la plateforme ENSAH</h1>
                <p>Votre compte vacataire a été créé</p>
            </div>
            <div class='content'>
                <p>Bonjour <strong>$prenom $nom</strong>,</p>
                
                <p>Votre compte vacataire a été créé par le coordinateur <strong>$coordinatorName</strong> pour la filière <strong>$filiereName</strong>.</p>
                
                <p><strong>Informations de votre compte :</strong></p>
                <ul>
                    <li><strong>Nom d'utilisateur :</strong> $username</li>
                    <li><strong>Rôle :</strong> Vacataire</li>
                    <li><strong>Filière :</strong> $filiereName</li>
                </ul>
                
                <p>Pour activer votre compte et définir votre mot de passe, veuillez cliquer sur le lien ci-dessous :</p>
                
                <p style='text-align: center;'>
                    <a href='$resetLink' class='button'>Initialiser mon mot de passe</a>
                </p>
                
                <p><span class='important'>Important :</span> Ce lien est valable pendant <strong>24 heures</strong> seulement.</p>
                
                <p>Après avoir initialisé votre mot de passe, vous pourrez vous connecter à la plateforme et accéder à toutes les fonctionnalités réservées aux vacataires.</p>
                
                <p>Si vous avez des questions, n'hésitez pas à contacter le coordinateur de votre filière.</p>
                
                <p>Cordialement,<br><strong>L'équipe ENSAH</strong></p>
            </div>
            <div class='footer'>
                <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
                <p>© " . date('Y') . " École Nationale des Sciences Appliquées Al Hoceima. Tous droits réservés.</p>
            </div>
        </div>
    </body>
    </html>";

    // Send the email
    return sendEmail($email, $subject, $message);
}

/**
 * Handle different HTTP methods and actions
 */
function handleRequest() {
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            createVacataireAccountAPI();
            break;
            
        default:
            jsonResponse(['error' => 'Méthode non autorisée'], 405);
            break;
    }
}

// Handle the request if this file is called directly
if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    handleRequest();
}
?>
